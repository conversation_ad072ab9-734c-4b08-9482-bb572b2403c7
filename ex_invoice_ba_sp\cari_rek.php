<?php
session_start();
include '../include/sapclasses/sap.php';
require_once '../security_helper.php';
sanitize_global_input();

// Set comprehensive security headers for XSS protection
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');
header('Referrer-Policy: strict-origin-when-cross-origin');
header('Permissions-Policy: geolocation=(), microphone=(), camera=()');

// Enhanced Content Security Policy for XSS protection
// Note: 'unsafe-inline' is needed for existing inline scripts/styles but should be removed in production
header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self'; connect-src 'self'; frame-ancestors 'none'; base-uri 'self'; form-action 'self'; object-src 'none'; media-src 'none';");

// Additional security headers
header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

$currentPage = "cari_rek.php";

// Validate and sanitize input parameters
$no_vendor = '';
if (isset($_REQUEST['no_vendor'])) {
    $no_vendor = validate_and_sanitize_output($_REQUEST['no_vendor'], 'html');
    // Additional validation for vendor number format
    if (!preg_match('/^[0-9A-Za-z\-_]{0,20}$/', $no_vendor)) {
        log_security_event('INVALID_VENDOR_NUMBER', 'Invalid vendor number format: ' . $no_vendor);
        $no_vendor = '';
    }
}

$isian = '';
$form_submitted = false;

// CSRF validation for form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['isian'])) {
    if (!isset($_POST['csrf_token']) || !validate_csrf_token($_POST['csrf_token'])) {
        log_security_event('CSRF_TOKEN_INVALID', 'Invalid or missing CSRF token');
        die('<div style="color: red; text-align: center; margin: 20px;">Security error: Invalid request. Please try again.</div>');
    }
    $form_submitted = true;
}

if(isset($_REQUEST['isian']) && $form_submitted){
	$isian = validate_and_sanitize_output($_REQUEST['isian'], 'html');
    // Additional validation for search input
    if (strlen($isian) > 100) {
        log_security_event('OVERSIZED_SEARCH_INPUT', 'Search input too long: ' . strlen($isian) . ' characters');
        $isian = substr($isian, 0, 100);
    }
	// Only proceed if we have valid input
	if (!empty($no_vendor) && !empty($isian)) {
		$sap = new SAPConnection();
		$sap->Connect("../include/sapclasses/logon_data.conf");
		if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
		if ($sap->GetStatus() != SAPRFC_OK ) {
		   // Don't expose SAP errors directly to prevent information disclosure
		   log_security_event('SAP_CONNECTION_ERROR', 'SAP connection failed');
		   echo "<div style='color: red; text-align: center;'>Connection error. Please contact administrator.</div>";
		   exit;
		}

		$fce = $sap->NewFunction ("Z_ZAPPSD_SEL_BANK");
		if ($fce == false ) {
		   log_security_event('SAP_FUNCTION_ERROR', 'SAP function creation failed');
		   echo "<div style='color: red; text-align: center;'>Function error. Please contact administrator.</div>";
		   exit;
		}

		//header entri - sanitize input before sending to SAP
		$fce->XLIFNR = sanitize_sap_input($no_vendor);

		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK ) {
			$fce->ZDATA->Reset();
			// Initialize arrays to prevent undefined variable warnings
			$lifnr = array();
			$banka = array();
			$brnch = array();
			$bankn = array();
			$bvtyp = array();

			while ( $fce->ZDATA->Next() ){
				// Sanitize data from SAP before storing
				$lifnr[]= validate_and_sanitize_output($fce->ZDATA->row["LIFNR"], 'html');
				$banka[]= validate_and_sanitize_output($fce->ZDATA->row["BANKA"], 'html');
				$brnch[]= validate_and_sanitize_output($fce->ZDATA->row["BRNCH"], 'html');
				$bankn[]= validate_and_sanitize_output($fce->ZDATA->row["BANKN"], 'html');
				$bvtyp[]= validate_and_sanitize_output($fce->ZDATA->row["BVTYP"], 'html');
			}
		} else {
			log_security_event('SAP_FUNCTION_CALL_ERROR', 'SAP function call failed');
			echo "<div style='color: red; text-align: center;'>Data retrieval error. Please contact administrator.</div>";
		}

		$fce->Close();
		$sap->Close();
	} else {
		// Initialize empty arrays if no valid input
		$lifnr = array();
		$banka = array();
		$brnch = array();
		$bankn = array();
		$bvtyp = array();
	}
}

?>
<script type="text/javascript">

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")

</script>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<link href="../css/tombol.css" rel="stylesheet" type="text/css" />
<style type="text/css">
<!--
#Layer1 {
	position:absolute;
	width:795px;
	height:115px;
	z-index:0;
	left: 159px;
	top: 296px;
}
.style5 {color: #791800}
-->
</style>
<head>
<script>
function setForm() {
	var btn = document.getElementById("cekdata"); 
	if(btn.value != 0){
	var kenya=btn.value;
	var bvtyp='bvtyp'+kenya;
	var komponen_bvtyp=document.getElementById(bvtyp); 
	var acc_br='acc_branch'+kenya;
	var komponen_acc_br=document.getElementById(acc_br); 
	var acc_no='acc_no'+kenya;
	var komponen_acc_no=document.getElementById(acc_no); 
	var acc_id='acc_id'+kenya;
	var cek;
	var komponen_acc_id=document.getElementById(acc_id); 
	opener.document.getElementById("no_rek").value = komponen_acc_id.value;
	opener.document.getElementById("nama_bank").value = komponen_acc_no.value;
	opener.document.getElementById("cabang_bank").value = komponen_acc_br.value;
	opener.document.getElementById("bvtyp").value = komponen_bvtyp.value;

    self.close();
	}else
		{
			alert('Pilih Data Rekening Dahulu')
			return false;
		}
}

function checkForother(obj) {  
	if (!document.layers) { 
	var kenya=obj.value;
	var btn = document.getElementById("cekdata"); 
	btn.value = kenya;
    //opener.document.dataformkaryawan.relation_karyawan.value = btn.value;
	} 
} 
</script>
<style type="text/css">
body	{background:#fff;}
table	{border:0;border-collapse:collapse;}
td		{padding:4px;}
tr.odd1	{background:#F9F9F9;}
tr.odd0	{background:#FFFFFF;}
tr.highlight	{background:#BDA9A2;}
tr.selected		{background:orange;color:#fff;}
</style>

<script type="text/javascript">
function addLoadEvent(func) {
  var oldonload = window.onload;
  if (typeof window.onload != 'function') {
    window.onload = func;
  } else {
    window.onload = function() {
      oldonload();
      func();
    }
  }
}

function addClass(element,value) {
  if (!element.className) {
    element.className = value;
  } else {
    newClassName = element.className;
    newClassName+= " ";
    newClassName+= value;
    element.className = newClassName;
  }
}

function removeClassName(oElm, strClassName){
	var oClassToRemove = new RegExp((strClassName + "\s?"), "i");
	oElm.className = oElm.className.replace(oClassToRemove, "").replace(/^\s?|\s?$/g, "");
}


function stripeTables() {
	var tables = document.getElementsByTagName("table");
	for (var m=0; m<tables.length; m++) {
		if (tables[m].className == "pickme") {
			var tbodies = tables[m].getElementsByTagName("tbody");
			for (var i=0; i<tbodies.length; i++) {
				var odd = true;
				var rows = tbodies[i].getElementsByTagName("tr");
				for (var j=0; j<rows.length; j++) {
					if (odd == false) {
						odd = true;
					} else {
						addClass(rows[j],"odd");
						odd = false;
					}
				}
			}
		}
	}
}
function highlightRows() {
  if(!document.getElementsByTagName) return false;
  	var tables = document.getElementsByTagName("table");
	for (var m=0; m<tables.length; m++) {
		if (tables[m].className == "pickme") {
			  var tbodies = tables[m].getElementsByTagName("tbody");
			  for (var j=0; j<tbodies.length; j++) {
				 var rows = tbodies[j].getElementsByTagName("tr");
				 for (var i=0; i<rows.length; i++) {
					   rows[i].oldClassName = rows[i].className
					   rows[i].onmouseover = function() {
						  if( this.className.indexOf("selected") == -1)
							 addClass(this,"highlight");
					   }
					   rows[i].onmouseout = function() {
						  if( this.className.indexOf("selected") == -1)
							 this.className = this.oldClassName
					   }
				 }
			  }
		}
	}
}

function selectRowRadio(row) {
	var radio = row.getElementsByTagName("input")[0];
	radio.checked = true;
	checkForother(radio);
}

function removeSelectedStateFromOtherRows() {
	var tables = document.getElementsByTagName("table");
	for (var m=0; m<tables.length; m++) {
		if (tables[m].className == "pickme") {
			var tbodies = tables[m].getElementsByTagName("tbody");
			for (var j=0; j<tbodies.length; j++) {
				var rows = tbodies[j].getElementsByTagName("tr");
				for (var i=0; i<rows.length; i++) {
					if (rows[i].className.indexOf("selected") != -1) {
						removeClassName(rows[i], "selected");
						removeClassName(rows[i], "highlight");
					}
				}
			}
		}
	}
}

function lockRow() {
  	var tables = document.getElementsByTagName("table");
	for (var m=0; m<tables.length; m++) {
		if (tables[m].className == "pickme") {
			var tbodies = tables[m].getElementsByTagName("tbody");
			for (var j=0; j<tbodies.length; j++) {
				var rows = tbodies[j].getElementsByTagName("tr");
				for (var i=0; i<rows.length; i++) {
					rows[i].oldClassName = rows[i].className;
					rows[i].onclick = function() {
						if (this.className.indexOf("selected") != -1) {
							this.className = this.oldClassName;
						} else {
							removeSelectedStateFromOtherRows();
							addClass(this,"selected");
						}
						selectRowRadio(this);
					}
				}
			}
		}
	}
}

addLoadEvent(stripeTables);
addLoadEvent(highlightRows);
addLoadEvent(lockRow);


function lockRowUsingRadio() {
	var tables = document.getElementsByTagName("table");
	for (var m=0; m<tables.length; m++) {
		if (tables[m].className == "pickme") {
			var tbodies = tables[m].getElementsByTagName("tbody");
			for (var j=0; j<tbodies.length; j++) {
				var radios = tbodies[j].getElementsByTagName("input");
				for (var i=0; i<radios.length; i++) {
					radios[i].onclick = function(evt) {
						if (this.parentNode.parentNode.className.indexOf("selected") != -1){
							this.parentNode.parentNode.className = this.parentNode.parentNode.oldClassName;
						} else {
							removeSelectedStateFromOtherRows();
							addClass(this.parentNode.parentNode,"selected");
						}
						if (window.event && !window.event.cancelBubble) {
							window.event.cancelBubble = "true";
						} else {
							evt.stopPropagation();
						}
					}
				}
			}
		}
	}
}
addLoadEvent(lockRowUsingRadio);
</script>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Daftar Data Rekening</title>
</head>

<body>
<div align="center">
<table width="800" align="center" class="adminheading" border="0">
<tr>
<th class="da2">Daftar Data Rekening </th>
</tr></table></div>

<?php
// Generate CSRF token for form protection
$csrf_token = generate_csrf_token();
?>
<form  id="form1" name="form1" method="post" action="<?php echo secure_output($currentPage); ?>">
		<!-- CSRF Protection -->
		<input type="hidden" name="csrf_token" value="<?php echo secure_output($csrf_token); ?>" />
		<input type="hidden" name="no_vendor" value="<?php echo secure_output($no_vendor); ?>" />

		<table width="800" align="center" class="adminform">
		<tr height="30">
		  <td class="puso">&nbsp;</td>
		  <td class="puso">&nbsp;</td>
		  <td>&nbsp;</td>
		  </tr>
		<tr>
		<td width="173" class="puso">Filter Data </td>
		<td width="26" class="puso">:</td>
		<td width="585"><input name="isian" type="text" class="" value="<?php echo secure_output($isian); ?>" size="40" maxlength="100"/>
		&nbsp;&nbsp;</td>
		</tr>
		<tr>
		<td>&nbsp;</td>
		<td>&nbsp;</td>
		<td rowspan="2">
		<input name="Submit" type="submit" class="button" value="Show" />
		<?php if (isset($org)): ?>
		<input name="org" id="org" type="hidden"  value="<?php echo secure_output($org); ?>" />
		<?php endif; ?>
		</td>
		</tr>
		<tr>
		<td>&nbsp;</td>
		<td>&nbsp;</td>
		</tr>
		</table>
</form>
<?php
if(isset($_POST['isian']) && $form_submitted){
$total = isset($lifnr) ? count($lifnr) : 0;
	if($total > 0){
?>
		<p></p>
		<div align="center">
		<table width="800" align="center" class="adminlist">
		<tr>
		<th align="left" colspan="4"> <span class="style5">&nbsp;Tabel Data Rekening </span> </th>
		</tr>
		</table>
		</div>
		<div align="center">
		<!-- This form is for display only, no CSRF needed as it doesn't submit data -->
		<form  name="formKaryawan">
	<table id="test1" width="800" align="center" class="pickme">
	<thead >
		  <tr class="quote">
			<td><div align="center"><strong>&nbsp;&nbsp; Cek.</strong></div></td>
			<td align="center"><strong>Nama Bank  </strong></td>
			<td align="center"><strong>Cabang </strong></td>
			<td align="center"><strong>No Rekening </strong></td>
			</tr>
		  </thead>
		  <tbody >
		  <?php for($i=0; $i<$total; $i++) {
				if(($i % 2) == 0)	{
				echo "<tr class='odd0'>";
					}
				else	{
				echo "<tr class='odd1'>";
					}
			$b = $i + 1;
			$acc_id = "acc_id" . $b;
			$acc_no = "acc_no" . $b;
			$acc_branch = "acc_branch" . $b;
			$bvtypke = "bvtyp" . $b;

			// Secure all output data
			$safe_b = secure_output($b);
			$safe_acc_id = secure_output($acc_id);
			$safe_acc_no = secure_output($acc_no);
			$safe_acc_branch = secure_output($acc_branch);
			$safe_bvtypke = secure_output($bvtypke);
			$safe_bankn = secure_output($bankn[$i]);
			$safe_bvtyp = secure_output($bvtyp[$i]);
			$safe_brnch = secure_output($brnch[$i]);
			$safe_banka = secure_output($banka[$i]);
			?>
			<td align="center"><input name="radiokaryawan" type="radio" value="<?php echo $safe_b; ?>" onChange="checkForother(this)" id="<?php echo $safe_b; ?>"/>
			<input id="<?php echo $safe_acc_id; ?>" name="<?php echo $safe_acc_id; ?>" type="hidden" value="<?php echo $safe_bankn; ?>" />
			<input id="<?php echo $safe_bvtypke; ?>" name="<?php echo $safe_bvtypke; ?>" type="hidden" value="<?php echo $safe_bvtyp; ?>" />
			<input id="<?php echo $safe_acc_branch; ?>" name="<?php echo $safe_acc_branch; ?>" type="hidden" value="<?php echo $safe_brnch; ?>" />
			<input id="<?php echo $safe_acc_no; ?>" name="<?php echo $safe_acc_no; ?>" type="hidden" value="<?php echo $safe_banka; ?>" />	</td>
			<td align="center"><?php echo $safe_banka; ?></td>
			<td align="center"><?php echo $safe_brnch; ?></td>
			<td align="center"><?php echo $safe_bankn; ?></td>
			</tr>
		  <?php } ?>
	  </tbody>
		</table>
		</div>
		<div align="center">
	<?php
	} else {
		$komen = "Tidak Ada Data Yang Di Temukan..";
	}
	?>
  <br />
  <br />
  <input type="button" value="Oke" name="kartu" class="button" onClick="setForm()">
    <input type="button" name="Submit2" value="Cancel" onClick="window.close();" class="button" />
    <input id="cekdata" name="cekdata" type="hidden" value="0" />
    <?php if (isset($org)): ?>
    <input id="org" name="org" type="hidden" value="<?php echo secure_output($org); ?>" />
    <?php endif; ?>
</div>
</form>

<div align="center">
<?php
if (isset($komen)) {
    echo secure_output($komen);
}
}
?></div>
<p>&nbsp;</p>
</p>
<?php include '../include/ekor.php'; ?>

</body>
</html>
